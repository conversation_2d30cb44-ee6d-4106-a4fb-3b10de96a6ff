# 🚀 Nexus Engine Quick Start Guide

Get up and running with the Nexus Engine Bluetooth Mesh Web3 Gaming Platform in minutes!

## 📋 Prerequisites

- **Rust 1.70+** with Cargo
- **Modern web browser** (Chrome, Firefox, Safari, Edge)
- **Python 3.7+** (for demo script)
- **Bluetooth adapter** (optional, for actual mesh networking)

## ⚡ Quick Setup

### 1. Clone and Build

```bash
git clone https://github.com/your-org/nexus-engine.git
cd nexus-engine
cargo build --release
```

### 2. Start the Engine

```bash
# Start with default configuration
cargo run

# Or with debug logging
RUST_LOG=debug cargo run
```

You should see:
```
INFO nexus_engine: Starting Nexus Engine - Bluetooth Mesh Web3 Gaming Platform
INFO nexus_engine: Configuration loaded successfully
INFO nexus_engine: Node identity loaded: a1b2c3d4...
INFO nexus_engine: Enhanced IPC Server listening on: ws://127.0.0.1:9898
INFO nexus_engine: Nexus Engine fully initialized and running
```

### 3. Open the Web Interface

1. Open your web browser
2. Navigate to the project directory
3. Open `AuraVisualizer.html`
4. You should see the 3D visualization connect automatically

### 4. Run the Demo

In a new terminal:

```bash
python3 demo.py
```

This will simulate:
- Bluetooth mesh peer connections
- Offline game session with multiple players
- Transaction queuing and blockchain synchronization

## 🎮 What You'll See

### 3D Visualization
- **Central Node**: Your Nexus Engine instance (glowing sphere)
- **Mesh Peers**: Connected devices around the perimeter
- **Game Players**: Cone-shaped objects representing active players
- **Transaction Queue**: Small cubes orbiting the central node
- **Connection Lines**: Show active mesh connections

### Status Panels
- **System Status**: Engine mode, peer counts, transaction queue
- **Network Status**: Mesh peers, Ronin blockchain connection
- **Game Status**: Active players and game session info
- **Detailed View**: Expandable panel with comprehensive statistics

### Interactive Controls
- **Engine Toggle**: Start/stop the engine
- **Mesh Mode**: Switch between P2P and Bluetooth mesh
- **Force Sync**: Manually trigger blockchain synchronization
- **Show/Hide Details**: Toggle detailed statistics panel

## 🔧 Configuration

Edit `Settings.toml` to customize:

```toml
# Basic settings
ipc_port = 9898
p2p_port = 4001

# Bluetooth mesh
[mesh]
max_peers = 8
scan_interval_ms = 1000

# Ronin blockchain
[ronin]
rpc_url = "https://api.roninchain.com/rpc"
chain_id = 2020

# Game settings
[game]
max_players = 4
sync_interval_ms = 100
```

## 🎯 Key Features to Try

### 1. Mesh Networking
- Click "Mesh Mode" to enable Bluetooth mesh
- Watch the visualization change to green (mesh active)
- See peer connections in the detailed panel

### 2. Game Simulation
- Run `python3 demo.py` to simulate gameplay
- Watch players appear in the 3D visualization
- Monitor game actions in the detailed statistics

### 3. Blockchain Integration
- Observe transaction queue building up
- Click "Force Sync" to simulate Ronin synchronization
- Watch pending transactions decrease

### 4. Real-time Updates
- All changes appear instantly in the 3D interface
- Status panels update automatically
- Connection quality affects visual representation

## 🐛 Troubleshooting

### Engine Won't Start
```bash
# Check if port 9898 is available
netstat -an | grep 9898

# Try a different port
sed -i 's/ipc_port = 9898/ipc_port = 9899/' Settings.toml
```

### Web Interface Won't Connect
1. Ensure the engine is running
2. Check browser console for WebSocket errors
3. Try refreshing the page
4. Verify firewall isn't blocking port 9898

### Demo Script Fails
```bash
# Install websockets if missing
pip3 install websockets

# Check Python version
python3 --version  # Should be 3.7+
```

### Bluetooth Issues
- Bluetooth mesh requires a compatible adapter
- On Linux, ensure `bluetoothd` is running
- On Windows, check Bluetooth drivers are installed
- macOS requires Bluetooth permissions

## 📚 Next Steps

### Development
1. **Add Custom Game Logic**: Extend `src/game_state.rs`
2. **Implement Smart Contracts**: Add Ronin contract interactions
3. **Create Mobile App**: Use the WebSocket API
4. **Add More Visualizations**: Extend the 3D interface

### Production Deployment
1. **Configure Ronin Mainnet**: Update `Settings.toml`
2. **Set Up SSL/TLS**: For secure WebSocket connections
3. **Deploy to Cloud**: Use Docker or cloud services
4. **Monitor Performance**: Add metrics and logging

### Testing
```bash
# Run all tests
cargo test

# Run specific test
cargo test test_mesh_topology

# Run with logging
RUST_LOG=debug cargo test
```

## 🤝 Getting Help

- **Documentation**: Check the main README.md
- **Issues**: [GitHub Issues](https://github.com/your-org/nexus-engine/issues)
- **Discussions**: [GitHub Discussions](https://github.com/your-org/nexus-engine/discussions)
- **Examples**: See `tests/integration_tests.rs`

## 🎉 Success!

If you can see the 3D visualization updating in real-time and the demo script runs successfully, you're ready to start building amazing offline Web3 games with Nexus Engine!

---

**Happy Gaming! 🎮⛓️📱**
